# AlbatrosDoc

A modern, multilingual website for AlbatrosDoc - your trusted partner for fast, secure and professional document procurement and delivery services.

## Features

- 🌍 **Multilingual Support**: Available in Bosnian (primary), English, and German
- 🎨 **Modern Design**: Clean, professional, and engaging user interface
- 📱 **Responsive**: Fully responsive design that works on all devices
- ⚡ **Fast Performance**: Built with Next.js 15 and optimized for speed
- 🎯 **Professional**: Designed to convey trust and reliability

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Internationalization**: next-intl
- **Deployment**: Ready for Vercel deployment

## Color Palette

- **Black**: #000000
- **Indigo Dye**: #1b3f5f
- **Moonstone**: #5c9ead
- **Ivory**: #f5fbef
- **Carmine**: #931621

## Getting Started

1. **Install dependencies**:
```bash
npm install
```

2. **Run the development server**:
```bash
npm run dev
```

3. **Open your browser** and navigate to [http://localhost:3000](http://localhost:3000)

## Language Support

The website supports three languages:
- **Bosnian (bs)** - Primary language
- **English (en)** - Secondary language
- **German (de)** - Secondary language

Language switching is available through the navigation bar.

## Project Structure

```
src/
├── app/
│   ├── [locale]/          # Locale-based routing
│   │   ├── layout.tsx     # Locale-specific layout
│   │   ├── page.tsx       # Homepage
│   │   └── globals.css    # Global styles
│   └── layout.tsx         # Root layout
├── components/
│   ├── Navigation.tsx     # Header with language switcher
│   ├── HeroSection.tsx    # Main hero section
│   ├── FeaturesSection.tsx # Features showcase
│   └── Footer.tsx         # Footer component
├── i18n/
│   └── request.ts         # i18n configuration
├── middleware.ts          # Next.js middleware for routing
└── messages/              # Translation files
    ├── bs.json           # Bosnian translations
    ├── en.json           # English translations
    └── de.json           # German translations
```

## Deployment

The easiest way to deploy this Next.js app is to use the [Vercel Platform](https://vercel.com/new):

1. Push your code to a Git repository
2. Import your project to Vercel
3. Vercel will automatically detect Next.js and deploy your app

## License

© 2024 AlbatrosDoc. All rights reserved.
