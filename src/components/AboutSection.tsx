'use client';

import { useTranslations } from 'next-intl';

const AboutSection = () => {
  const t = useTranslations('hero');

  return (
    <section className="py-32 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      {/* Sophisticated Background Elements */}
      <div className="absolute inset-0">
        {/* Geometric shapes inspired by the designs */}
        <div className="absolute top-20 right-20 w-64 h-64 border border-gray-200 rounded-full opacity-30"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 border border-blue-200 rounded-full opacity-40"></div>

        {/* Gradient overlays */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-100/50 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-gray-100/50 to-transparent rounded-full blur-3xl"></div>

        {/* Connecting lines */}
        <svg className="absolute inset-0 w-full h-full opacity-20" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="aboutLineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="rgba(59, 130, 246, 0.3)" />
              <stop offset="100%" stopColor="rgba(156, 163, 175, 0.2)" />
            </linearGradient>
          </defs>
          <path d="M 100 200 Q 400 100 600 300" stroke="url(#aboutLineGradient)" strokeWidth="1" fill="none" />
        </svg>
      </div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-20 items-center">
          {/* Content */}
          <div className="space-y-12">
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="inline-block px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-semibold">
                  O nama
                </div>
                <h2 className="text-5xl md:text-6xl font-black leading-tight text-gray-900">
                  Najbolje ideje
                  <span className="block bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent">
                    ne čekaju
                  </span>
                </h2>
              </div>

              <div className="space-y-6 text-lg leading-relaxed text-gray-600">
                <p className="text-xl">
                  {t('subtitle')}
                </p>

                <p>
                  {t('mission')}
                </p>

                <p className="text-xl font-medium text-gray-900 border-l-4 border-blue-500 pl-6">
                  {t('vision')}
                </p>
              </div>
            </div>

            <div className="pt-4">
              <p className="text-xl font-semibold text-blue-600 mb-8">
                {t('promise')}
              </p>

              <p className="text-lg text-gray-500 italic">
                {t('cta')}
              </p>
            </div>
          </div>

          {/* Visual Element - Modern Card Layout */}
          <div className="relative">
            <div className="space-y-8">
              {/* Main Feature Card */}
              <div className="bg-gradient-to-br from-slate-900 to-blue-900 rounded-3xl p-10 text-white relative overflow-hidden group hover:scale-105 transition-all duration-500">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative z-10">
                  <div className="flex items-center space-x-4 mb-6">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-cyan-300 rounded-2xl flex items-center justify-center">
                      <svg className="w-8 h-8 text-slate-900" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold">Brzina & Efikasnost</h3>
                      <p className="text-blue-200">Dokumenti u najkraćem roku</p>
                    </div>
                  </div>
                  <p className="text-lg text-gray-300 leading-relaxed">
                    Naša tehnologija omogućava brzu obradu dokumenata uz održavanje najviših standarda kvaliteta.
                  </p>
                </div>
              </div>

              {/* Secondary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 group hover:scale-105">
                  <div className="space-y-4">
                    <div className="w-14 h-14 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Sigurnost</h3>
                    <p className="text-gray-600">Potpuna diskrecija i zaštićena komunikacija</p>
                  </div>
                </div>

                <div className="bg-white rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 border border-gray-100 group hover:scale-105">
                  <div className="space-y-4">
                    <div className="w-14 h-14 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Profesionalnost</h3>
                    <p className="text-gray-600">Maksimalna preciznost u svakom koraku</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
