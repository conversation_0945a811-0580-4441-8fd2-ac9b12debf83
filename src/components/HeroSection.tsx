'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import gradientGL from 'gradient-gl';

const HeroSection = () => {
  const t = useTranslations('hero');

  useEffect(() => {
    // Initialize gradient-gl with the specified seed and target the gradient container
    gradientGL('f2.fc78', '#hero-gradient-bg');
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* WebGL Gradient Background */}
      <div id="hero-gradient-bg" className="absolute inset-0 z-0"></div>
      <div className="bg-albatros-indigo-dye absolute left-0 top-0 w-full h-full opacity-70"></div>

      <div className="relative w-full max-w-7xl mx-auto px-6 lg:px-8 z-20">
        <div className="py-32 space-y-16">
          {/* Main Title - Bold and Impactful */}
          <div className="space-y-8">
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold leading-[0.9] tracking-tight">
              <span className="block max-w-4xl text-white mb-4">
                {t('title')}
              </span>
            </h1>

            <div className="max-w-3xl">
              <p className="text-xl md:text-2xl text-blue-100/90 leading-relaxed font-light">
                {t('description')}
              </p>
            </div>
          </div>

          {/* Enhanced CTA with modern styling */}
          <div className="flex flex-col sm:flex-row gap-6 pt-8">
            <button className="group relative bg-gradient-to-r from-blue-500 to-cyan-400 text-white px-10 py-4 rounded-full font-semibold text-lg hover:shadow-2xl hover:shadow-blue-500/25 transform hover:scale-105 transition-all duration-300 overflow-hidden">
              <span className="relative z-10">Kontaktirajte nas</span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>

            <button className="group flex items-center space-x-3 text-white/80 hover:text-white font-medium transition-all duration-300">
              <div className="w-12 h-12 border border-white/30 rounded-full flex items-center justify-center group-hover:border-white/60 transition-all duration-300">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Pogledajte video</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
