import createMiddleware from 'next-intl/middleware';

export default createMiddleware({
  // A list of all locales that are supported
  locales: ['bs', 'en', 'de'],

  // Used when no locale matches
  defaultLocale: 'bs',

  // Don't use a prefix for the default locale
  localePrefix: {
    mode: 'as-needed'
  },

  // Disable locale cookie to prevent it from overriding the default
  localeCookie: false,

  // Disable automatic locale detection from browser headers
  localeDetection: false
});

export const config = {
  // Match only internationalized pathnames
  // Include /bs to handle redirects to root, and /de, /en for other locales
  matcher: ['/', '/(bs|de|en)/:path*']
};
