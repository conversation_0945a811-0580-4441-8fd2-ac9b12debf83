import e from"../server/react-server/getConfigNow.js";import r from"../server/react-server/getFormats.js";import t from"../shared/NextIntlClientProvider.js";import{jsx as o}from"react/jsx-runtime";import s from"../server/react-server/getTimeZone.js";import a from"../server/react-server/getMessages.js";import m from"../server/react-server/getLocale.js";async function i({formats:i,locale:n,messages:v,now:f,timeZone:c,...g}){return o(t,{formats:void 0===i?await r():i,locale:n??await m(),messages:void 0===v?await a():v,now:f??await e(),timeZone:c??await s(),...g})}export{i as default};
