{"name": "gradient-gl", "description": "Tiny WebGL library for procedural gradient animations. Deterministic. Seed-driven.", "version": "1.4.2", "bugs": "https://github.com/metaory/gradient-gl/issues", "homepage": "https://metaory.github.io/gradient-gl/", "repository": "git+https://github.com/metaory/gradient-gl.git", "type": "module", "keywords": ["webgl", "gradient", "animation", "background", "shader", "seed", "deterministic", "canvas"], "exports": {".": "./index.js"}, "files": ["LICENSE", "README.md", "index.js", "shaders/*", "docs/public/*", "package.json"], "browserslist": ["last 2 Chrome versions", "last 2 Firefox versions", "last 2 Safari versions", "last 2 Edge versions"], "author": "metaory <<EMAIL>>", "license": "MIT", "scripts": {"prepare": "ln -sf ../index.js docs/ && ln -sf ../shaders docs/", "dev": "npm run prepare && npx vite docs"}}