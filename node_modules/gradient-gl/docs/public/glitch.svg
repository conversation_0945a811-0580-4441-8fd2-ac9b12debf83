<svg xmlns="http://www.w3.org/2000/svg"
     viewBox="0 0 187 32" >
      <!-- made with glitcher-cli v0.7
        █▀▀ █░░ █ ▀█▀ █▀▀ █░█ █▀▀ █▀█
        █▄█ █▄▄ █ ░█░ █▄▄ █▀█ ██▄ █▀▄
      https://github.com/metaory/glitcher-cli -->
      <!-- <style> svg { background-color: indigo; } </style> -->
      
      <text filter="url(#glitch)"
            fill="#FFFFFF"
            font-family="monospace, serif"
            font-weight="bolder"
            font-size="x-large"
            text-anchor="middle"
            lengthAdjust="spacingAndGlyphs"
            textLength="70%"
            dominant-baseline="mathematical"
            x="50%" y="30%"
      >gradient-gl</text>
      <defs>
      <filter id="glitch"
              primitiveUnits="objectBoundingBox"
              x="0%"
              y="0%"
              height="100%">
          <feColorMatrix in="SourceGraphic"
                         result="red"
                         type="matrix"
                         values="1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0" />
          <feColorMatrix in="SourceGraphic"
                         result="green"
                         type="matrix"
                         values="0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 1 0" />
          <feColorMatrix in="SourceGraphic"
                         result="blue"
                         type="matrix"
                         values="0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 1 0" />
          <feOffset in="red"
                    result="red-shifted"
                    dx="-0.01"
                    dy="0">
          <animate attributeName="dx"
                   keyTimes="0; 0.11; 0.22; 0.23; 0.37; 0.56; 0.60; 0.61; 0.79; 0.87"
                   values="0.01; 0.00; 0.01; -0.01; -0.01; 0.00; 0.02; 0.01; 0.01; -0.01"
                   begin="0"
                   dur="57.06s"
                   calcMode="discrete"
                   repeatCount="indefinite"
                   fill="freeze" />
            </feOffset>
            <feOffset in="blue"
                      result="blue-shifted"
                      dx="0.01"
                      dy="0">
                <animate attributeName="dx"
                         keyTimes="0; 0.14; 0.18; 0.34; 0.44; 0.49; 0.68; 0.70; 0.81; 0.85; 0.86"
                         values="-0.00; 0.00; -0.01; 0.00; 0.01; 0.01; 0.01; 0.01; 0.00; 0.00; 0.01"
                         begin="0"
                         dur="52.27s"
                         calcMode="discrete"
                         repeatCount="indefinite"
                         fill="freeze" />
            </feOffset>
            <feBlend mode="screen"
                     in="red-shifted"
                     in2="green"
                     result="red-green" />
            <feBlend mode="screen"
                     in="red-green"
                     in2="blue-shifted"
                     result="blended" />
    <feOffset in="blended"
              dx="0"
              dy="0"
              y="0%"
              height="23.29%"
              result="slice_1a" />
    <feOffset in="blended"
              dx="0"
              dy="0"
              y="23.29%"
              height="12.67%"
              result="slice_1b">
        <animate attributeName="dx"
                 keyTimes="0; 0.09; 0.11; 0.31; 0.49; 0.54; 0.58; 0.76; 0.87"
                 values="0.01; -0.00; -0.00; -0.01; 0.01; -0.01; -0.00; -0.01; 0.01"
                 begin="0s"
                 dur="40.80s"
                 calcMode="discrete"
                 repeatCount="indefinite"
                 fill="freeze" />
    </feOffset>
    <feOffset in="blended"
              dx="0"
              dy="0"
              y="35%"
              height="23.97%"
              result="slice_2a" />
    <feOffset in="blended"
              dx="0"
              dy="0"
              y="58.97%"
              height="15.95%"
              result="slice_2b">
        <animate attributeName="dx"
                 keyTimes="0; 0.16; 0.28; 0.31; 0.32; 0.45; 0.60; 0.75; 0.96"
                 values="-0.01; -0.00; -0.00; -0.00; -0.00; -0.00; -0.00; 0.00; 0.00"
                 begin="0s"
                 dur="48.21s"
                 calcMode="discrete"
                 repeatCount="indefinite"
                 fill="freeze" />
    </feOffset>
    <feOffset in="blended"
              dx="0"
              dy="0"
              y="74%"
              height="20.23%"
              result="slice_3a" />
    <feOffset in="blended"
              dx="0"
              dy="0"
              y="94.23%"
              height="8.19%"
              result="slice_3b">
        <animate attributeName="dx"
                 keyTimes="0; 0.32; 0.35; 0.38; 0.74; 0.75; 0.76"
                 values="0.00; -0.01; -0.01; -0.00; -0.01; -0.00; 0.00"
                 begin="0s"
                 dur="42.77s"
                 calcMode="discrete"
                 repeatCount="indefinite"
                 fill="freeze" />
    </feOffset>
    <feMerge>
        <feMergeNode in="slice_1a" />
        <feMergeNode in="slice_1b" />
        <feMergeNode in="slice_2a" />
        <feMergeNode in="slice_2b" />
        <feMergeNode in="slice_3a" />
        <feMergeNode in="slice_3b" />
    </feMerge>
    </filter>
    </defs>
</svg>
