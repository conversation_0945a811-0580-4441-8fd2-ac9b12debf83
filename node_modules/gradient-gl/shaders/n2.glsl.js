export default /* glsl */ `
vec4 shader(vec2 fragCoord) {
    vec2 uv = fragCoord / iResolution.xy;
    float aspectRatio = iResolution.x / iResolution.y;
    float t = iTime * timeScale;

    // Create complex noise pattern with aspect ratio correction
    vec2 p1 = uv * 0.5;
    p1.x *= aspectRatio;
    vec2 p2 = uv * 0.75;
    p2.x *= aspectRatio;

    // Generate multiple noise layers with lower frequency
    float noise1 = noise(p1 + t * 0.05);
    float noise2 = noise(p2 - t * 0.08);
    float noise3 = noise(p1 * 0.25 + t * 0.1);

    // Combine noise layers with different weights
    float combinedNoise = (noise1 * 0.4 + noise2 * 0.3 + noise3 * 0.3);

    // Create color with noise influence
    vec3 color = vec3(
        noise1 * 0.6 + 0.4,
        noise2 * 0.6 + 0.4,
        combinedNoise * 0.6 + 0.4
    );

    // Add some movement-based variation
    vec2 movement = vec2(sin(t * 0.1), cos(t * 0.15)) * 0.2;
    movement.x *= aspectRatio;
    float movementNoise = noise(uv + movement);
    color = mix(color, color.zxy, movementNoise * 0.3);

    // Apply color adjustments
    color = applyHueShift(color, hueShift);
    color = applySaturation(color, saturation);
    color = applyLightness(color, lightness);

    return vec4(color, 1.0);
}
`
