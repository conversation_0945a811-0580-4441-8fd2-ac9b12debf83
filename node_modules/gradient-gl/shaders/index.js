import a1 from './a1.glsl.js'
import a2 from './a2.glsl.js'
import b1 from './b1.glsl.js'
import b2 from './b2.glsl.js'
import b3 from './b3.glsl.js'
import b4 from './b4.glsl.js'
import b5 from './b5.glsl.js'
import f1 from './f1.glsl.js'
import f2 from './f2.glsl.js'
import f3 from './f3.glsl.js'
import n1 from './n1.glsl.js'
import n2 from './n2.glsl.js'

export const shaders = {
  a1, a2, b1, b2, b3, b4, b5, f1, f2, f3, n1, n2
}

export default Object.keys(shaders)
