"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[798],{901:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return i}});let i=o(8229)._(o(2115)).default.createContext(null)},1193:(e,t)=>{function o(e){var t;let{config:o,src:i,width:r,quality:a}=e,n=a||(null==(t=o.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return o.path+"?url="+encodeURIComponent(i)+"&w="+r+"&q="+n+(i.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),o.__next_img_default=!0;let i=o},1469:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var o in t)Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}(t,{default:function(){return s},getImageProps:function(){return l}});let i=o(8229),r=o(8883),a=o(3063),n=i._(o(1193));function l(e){let{props:t}=(0,r.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,o]of Object.entries(t))void 0===o&&delete t[e];return{props:t}}let s=a.Image},2272:(e,t,o)=>{o.d(t,{A:()=>d});let i=`#version 300 es
precision highp float;
out vec4 fragColor;

uniform vec3 iResolution;
uniform float iTime;
uniform float iFrame;
uniform float timeScale;
uniform float hueShift;
uniform float saturation;
uniform float lightness;

#define POINTS 32
#define PI 3.1415926536
#define TAU (2.0 * PI)
#define S(a,b,t) smoothstep(a,b,t)

mat2 rot(float a) {
    float s = sin(a);
    float c = cos(a);
    return mat2(c, -s, s, c);
}

// HSV to RGB conversion
vec3 hsv2rgb(vec3 c) {
    vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
    vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
    return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
}

// RGB to HSV conversion
vec3 rgb2hsv(vec3 c) {
    vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
    vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
    vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));

    float d = q.x - min(q.w, q.y);
    float e = 1.0e-10;
    return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

// Apply hue shift to RGB color
vec3 applyHueShift(vec3 color, float shift) {
    vec3 hsv = rgb2hsv(color);
    hsv.x = fract(hsv.x + shift); // Rotate hue by shift amount (0-1 range)
    return hsv2rgb(hsv);
}

// Apply saturation adjustment to RGB color
vec3 applySaturation(vec3 color, float satFactor) {
    vec3 hsv = rgb2hsv(color);
    hsv.y = clamp(hsv.y * satFactor, 0.0, 1.0); // Adjust saturation
    return hsv2rgb(hsv);
}

// Add dithering function
float dither(vec2 uv) {
    return fract(sin(dot(uv, vec2(12.9898, 78.233))) * 43758.5453);
}

// Apply lightness adjustment to RGB color
vec3 applyLightness(vec3 color, float lightFactor) {
    // Convert to grayscale for more dramatic effect
    float gray = dot(color, vec3(0.299, 0.587, 0.114));

    // Shift the curve to make 0 match previous 1
    float shiftedFactor = (lightFactor * 14.0 + 1.0) / 15.0;
    float curve = shiftedFactor * shiftedFactor * 0.9;

    // Mix between original color and white/black based on lightness
    vec3 result;
    if (lightFactor > 0.5) {
        // Mix with white for lighter values, but cap at 0.95
        float mixAmount = min((curve - 0.5) * 2.0, 0.95);
        result = mix(color, vec3(1.0), mixAmount);
    } else {
        // Mix with black for darker values, but cap at 0.95
        float mixAmount = min(curve * 2.0, 0.95);
        result = mix(vec3(0.1), color, mixAmount);
    }

    // Add dithering to break up color bands
    float ditherAmount = (1.0 - lightFactor) * 0.02; // More dither in darker areas
    vec2 uv = gl_FragCoord.xy / iResolution.xy;
    float noise = dither(uv) * ditherAmount;
    result += vec3(noise);

    return result;
}

vec2 hash(vec2 p) {
    p = vec2(dot(p, vec2(2127.1, 81.17)), dot(p, vec2(1269.5, 283.37)));
    return fract(sin(p)*43758.5453);
}

float noise(in vec2 p) {
    vec2 i = floor(p);
    vec2 f = fract(p);
    vec2 u = f*f*(3.0-2.0*f);
    float n = mix(mix(dot(-1.0+2.0*hash(i + vec2(0.0, 0.0)), f - vec2(0.0, 0.0)),
    dot(-1.0+2.0*hash(i + vec2(1.0, 0.0)), f - vec2(1.0, 0.0)), u.x),
    mix(dot(-1.0+2.0*hash(i + vec2(0.0, 1.0)), f - vec2(0.0, 1.0)),
    dot(-1.0+2.0*hash(i + vec2(1.0, 1.0)), f - vec2(1.0, 1.0)), u.x), u.y);
    return 0.5 + 0.5*n;
}
`,r={a1:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord / iResolution.xy;
  float aspectRatio = iResolution.x / iResolution.y;
  vec2 tuv = uv - .5;
  float t = iTime * timeScale;  // Use timeScale for dynamic speed
  float degree = noise(vec2(t * 0.1, tuv.x*tuv.y));  // Slow rotation
  tuv.y *= 1./aspectRatio;
  tuv *= rot(radians((degree-.5)*720.+180.));
  tuv.y *= aspectRatio;
  float frequency = 5.;
  float amplitude = 30.;
  float speed = t * 2.0;  // Use timeScale for speed
  tuv.x += sin(tuv.y*frequency+speed)/amplitude;
  tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);
  vec3 amberYellow = vec3(299, 186, 137) / vec3(255);
  vec3 deepBlue = vec3(49, 98, 238) / vec3(255);
  vec3 pink = vec3(246, 146, 146) / vec3(255);
  vec3 blue = vec3(89, 181, 243) / vec3(255);
  vec3 purpleHaze = vec3(105, 49, 245) / vec3(255);
  vec3 swampyBlack = vec3(32, 42, 50) / vec3(255);
  vec3 persimmonOrange = vec3(233, 51, 52) / vec3(255);
  vec3 darkAmber = vec3(233, 160, 75) / vec3(255);
  float cycle = sin(t * 0.5);  // Slower color cycling
  float mixT = (sign(cycle) * pow(abs(cycle), 0.6) + 1.) / 2.;
  vec3 color1 = mix(amberYellow, purpleHaze, mixT);
  vec3 color2 = mix(deepBlue, swampyBlack, mixT);
  vec3 color3 = mix(pink, persimmonOrange, mixT);
  vec3 color4 = mix(blue, darkAmber, mixT);
  vec3 layer1 = mix(color3, color2, smoothstep(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 layer2 = mix(color4, color1, smoothstep(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 color = mix(layer1, layer2, smoothstep(.5, -.3, tuv.y));

  // Apply hue shift to the final color
  color = applyHueShift(color, hueShift);

  // Apply saturation adjustment
  color = applySaturation(color, saturation);

  // Apply lightness adjustment
  color = applyLightness(color, lightness);

  return vec4(color, 1.0);
}
`,a2:`
vec3 hash3d(vec3 p) {
  p = vec3(dot(p, vec3(127.1, 311.7, 74.7)), dot(p, vec3(269.5, 183.3, 246.1)),
          dot(p, vec3(113.5, 271.9, 124.6)));
  p = -1.0 + 2.0 * fract(sin(p) * 43758.5453123);
  return p;
}

float noise3d(in vec3 p) {
  vec3 i = floor(p);
  vec3 f = fract(p);
  vec3 u = f * f * (3.0 - 2.0 * f);
  return mix(
      mix(mix(dot(hash3d(i + vec3(0.0, 0.0, 0.0)), f - vec3(0.0, 0.0, 0.0)),
              dot(hash3d(i + vec3(1.0, 0.0, 0.0)), f - vec3(1.0, 0.0, 0.0)),
              u.x),
          mix(dot(hash3d(i + vec3(0.0, 1.0, 0.0)), f - vec3(0.0, 1.0, 0.0)),
              dot(hash3d(i + vec3(1.0, 1.0, 0.0)), f - vec3(1.0, 1.0, 0.0)),
              u.x),
          u.y),
      mix(mix(dot(hash3d(i + vec3(0.0, 0.0, 1.0)), f - vec3(0.0, 0.0, 1.0)),
              dot(hash3d(i + vec3(1.0, 0.0, 1.0)), f - vec3(1.0, 0.0, 1.0)),
              u.x),
          mix(dot(hash3d(i + vec3(0.0, 1.0, 1.0)), f - vec3(0.0, 1.0, 1.0)),
              dot(hash3d(i + vec3(1.0, 1.0, 1.0)), f - vec3(1.0, 1.0, 1.0)),
              u.x),
          u.y),
      u.z);
}

vec4 shader(vec2 fragCoord) {
  const int layers = 5;
  const float baseSpeed = 0.25; // Base speed
  const float scale = 1.2;

  vec2 uv = (fragCoord - iResolution.xy - .5) / iResolution.y;
  float t = iTime * baseSpeed * timeScale; // Use timeScale for dynamic speed
  uv *= scale;
  float h =
      noise3d(vec3(uv * 2., t)); // Time as z-coordinate for continuous noise
  for (int n = 1; n < layers; n++) {
    float i = float(n);
    uv -= vec2(0.7 / i * sin(i * uv.y + i + t * 2.0 + h * i) +
                  0.8, // Reduced from 5.0 to 2.0
              0.4 / i * sin(uv.x + 4. - i + h + t * 2.0 + 0.3 * i) +
                  1.6); // Reduced from 5.0 to 2.0
  }
  uv -=
      vec2(1.2 * sin(uv.x + t + h) + 1.8, 0.4 * sin(uv.y + t + 0.3 * h) + 1.6);
  vec3 col = vec3(.5 * sin(uv.x) + 0.5, .5 * sin(uv.x + uv.y) + 0.5,
                  .5 * sin(uv.y) + 0.8) *
            0.8;

  // Apply hue shift to the final color
  col = applyHueShift(col, hueShift);

  // Apply saturation adjustment
  col = applySaturation(col, saturation);

  // Apply lightness adjustment
  col = applyLightness(col, lightness);

  return vec4(col, 1.0);
}
`,b1:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord.xy / iResolution.xy;
  vec2 p[4];
  p[0] = vec2(0.1, 0.9);
  p[1] = vec2(0.9, 0.9);
  p[2] = vec2(0.5, 0.1);
  float t = iTime * timeScale;  // Use timeScale for dynamic speed
  p[3] = vec2(cos(t), sin(t)) * 0.4 + vec2(0.5, 0.5);
  vec3 c[4];
  // Add subtle color animation
  float colorShift = sin(t * 0.2) * 0.1;  // Slow color cycling
  c[0] = vec3(0.996078431372549 + colorShift, 0.3411764705882353, 0.33725490196078434);
  c[1] = vec3(0.996078431372549, 0.6352941176470588 + colorShift, 0.1607843137254902);
  c[2] = vec3(0.1450980392156863, 0.8196078431372549, 0.8588235294117647 + colorShift);
  c[3] = vec3(1.0, 1.0, 0.0);
  float blend = 2.0;
  vec3 sum = vec3(0.0);
  float valence = 0.0;
  for (int i = 0; i < 4; i++) {
      float distance = length(uv - p[i]);
      if (distance == 0.0) { distance = 1.0; }
      float w =  1.0 / pow(distance, blend);
      sum += w * c[i];
      valence += w;
  }
  sum /= valence;
  sum = pow(sum, vec3(1.0/2.2));

  // Apply hue shift to the final color
  sum = applyHueShift(sum, hueShift);

  // Apply saturation adjustment
  sum = applySaturation(sum, saturation);

  // Apply lightness adjustment
  sum = applyLightness(sum, lightness);

  return vec4(sum.xyz, 1.0);
}
`,b2:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord/iResolution.xy;
  float ratio = iResolution.x / iResolution.y;
  vec2 tuv = uv;
  tuv -= .5;
  float t = iTime * timeScale;
  float degree = noise(vec2(t * 0.1, tuv.x*tuv.y));
  tuv.y *= 1./ratio;
  tuv *= rot(radians((degree-.5)*720.+180.));
  tuv.y *= ratio;
  float frequency = 5.;
  float amplitude = 30.;
  float speed = t * 1.0;
  tuv.x += sin(tuv.y*frequency+speed)/amplitude;
  tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);
  vec3 colorYellow = vec3(.957, .804, .623);
  vec3 colorDeepBlue = vec3(.192, .384, .933);
  vec3 layer1 = mix(colorYellow, colorDeepBlue, S(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 colorRed = vec3(.910, .510, .8);
  vec3 colorBlue = vec3(0.350, .71, .953);
  vec3 layer2 = mix(colorRed, colorBlue, S(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 finalComp = mix(layer1, layer2, S(.5, -.3, tuv.y));

  // Apply color adjustments
  finalComp = applyHueShift(finalComp, hueShift);
  finalComp = applySaturation(finalComp, saturation);
  finalComp = applyLightness(finalComp, lightness);

  return vec4(finalComp, 1.0);
}
`,b3:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = (fragCoord/iResolution.xy)*1.;
  uv.y -= 1.5;
  uv.x += .2;
  float t = iTime * timeScale;  // Use timeScale uniform
  vec2 p = uv;
  float t1 = t * 1.5;  // Reduced from 3.0 to 1.5
  float t2 = t * 0.5;  // Reduced from 1.0 to 0.5
  p.y *= (p.x*p.y) * sin(p.y*p.x + t1);  // Reduced frequency from 2. to 1.
  float d = length(p*.7);
  vec3 c0 = vec3(1.);
  vec3 c1 = vec3(.365, .794, .935);
  vec3 c2 = vec3(.973, .671, .961);
  vec3 c3 = vec3(.973, .843, .439);
  float offset = 1.2;
  float step1 = .05*offset + sin(t2*2.)*.1;  // Reduced from 3. to 2.
  float step2 = 0.3*offset + sin(t2)*.15;
  float step3 = 0.6*offset + sin(t2)*.1;
  float step4 = 1.2*offset + sin(t2*2.)*.2;  // Reduced from 3. to 2.
  vec3 col = mix(c0, c1, smoothstep(step1, step2, d));
  col = mix(col, c2, smoothstep(step2, step3, d));
  col = mix(col, c3, smoothstep(step3, step4, d));

  // Apply color adjustments
  col = applyHueShift(col, hueShift);
  col = applySaturation(col, saturation);
  col = applyLightness(col, lightness);

  return vec4(col, .5);
}
`,b4:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord/iResolution.xy;
  float ratio = iResolution.x / iResolution.y;
  vec2 tuv = uv;
  tuv -= .5;
  float t = iTime * timeScale;
  float degree = noise(vec2(t * 0.1, tuv.x*tuv.y));
  tuv.y *= 1./ratio;
  tuv *= rot(radians((degree-.5)*720.+75.));
  tuv.y *= ratio;
  float frequency = 2.;
  float amplitude = 30.;
  float speed = t * 1.0;
  tuv.x += sin(tuv.y*frequency+speed)/amplitude;
  tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);
  vec3 colorWhite = vec3(1.0, 1.0, 1.0);
  vec3 colorRed = vec3(.914, .345, .62);
  vec3 colorPurple = vec3(.792, .573, .871);
  vec3 colorGreen = vec3(.612, .91, .364);
  vec3 colorBlue = vec3(.42, .773, .937);
  vec3 colorYellow = vec3(1.0, .973, .325);
  vec3 layer1 = mix(colorRed, colorYellow, S(-.6, .2, (tuv*rot(radians(-5.))).x));
  layer1 = mix(layer1, colorWhite, S(-.6, .2, (tuv*rot(radians(-5.))).x));
  layer1 = mix(layer1, colorPurple, S(-.2, .6, (tuv*rot(radians(-5.))).x));
  vec3 layer2 = mix(colorRed, colorYellow, S(-.8, .2, (tuv*rot(radians(-5.))).x));
  layer2 = mix(layer2, colorGreen, S(-.1, .9, (tuv*rot(radians(-5.))).x));
  layer2 = mix(layer2, colorBlue, S(-.5, .5, (tuv*rot(radians(-5.))).x));
  vec3 finalComp = mix(layer1, layer2, S(.7, -.5, tuv.y));

  // Apply color adjustments
  finalComp = applyHueShift(finalComp, hueShift);
  finalComp = applySaturation(finalComp, saturation);
  finalComp = applyLightness(finalComp, lightness);

  return vec4(finalComp, 1.0);
}
`,b5:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord/iResolution.xy;
  float t = iTime * timeScale;

  // Create smooth rotation based on noise
  float degree = noise(vec2(t * 0.1, uv.x*uv.y));
  vec2 tuv = uv * 2.0 - 1.0;
  tuv *= 1.5;
  tuv *= rot(radians((degree-.5)*720.+180.));

  // Add wave distortion with adjusted scaling
  float frequency = 3.0;
  float amplitude = 40.0;
  float speed = t * 0.8;
  tuv.x += sin(tuv.y*frequency+speed)/amplitude;
  tuv.y += sin(tuv.x*frequency*1.5+speed)/(amplitude*.5);

  // Define a rich color palette
  vec3 color1 = vec3(0.957, 0.804, 0.623);
  vec3 color2 = vec3(0.192, 0.384, 0.933);
  vec3 color3 = vec3(0.910, 0.510, 0.800);
  vec3 color4 = vec3(0.350, 0.710, 0.953);

  // Create layered gradients with smooth transitions
  vec3 layer1 = mix(color1, color2, S(-.3, .2, (tuv*rot(radians(-5.))).x));
  vec3 layer2 = mix(color3, color4, S(-.3, .2, (tuv*rot(radians(-5.))).x));

  // Blend layers with smooth vertical transition
  vec3 finalColor = mix(layer1, layer2, S(.5, -.3, tuv.y));

  // Add subtle color variation based on time
  float colorShift = sin(t * 0.3) * 0.1;
  finalColor = mix(finalColor, finalColor.yzx, colorShift);

  // Add subtle vignette with wider coverage
  float vignette = smoothstep(1.0, 0.0, length(uv - 0.5));
  finalColor *= vignette;

  // Apply color adjustments
  finalColor = applyHueShift(finalColor, hueShift);
  finalColor = applySaturation(finalColor, saturation);
  finalColor = applyLightness(finalColor, lightness);

  return vec4(finalColor, 1.0);
}
`,f1:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord / iResolution.xy;
  float aspectRatio = iResolution.x / iResolution.y;
  float t = iTime * timeScale;

  // Create fluid-like movement with aspect ratio correction
  vec2 p = uv * 2.0 - 1.0;
  p.x *= aspectRatio;
  p *= 0.5; // Reduced scale for larger blobs

  // Add some noise-based distortion with lower frequency
  float noise1 = noise(p + t * 0.05); // Reduced time factor
  float noise2 = noise(p * 0.5 - t * 0.1); // Reduced scale and time factor

  // Create gradient with noise influence
  vec3 color = vec3(
    noise1 * 0.5 + 0.5,
    noise2 * 0.5 + 0.5,
    (noise1 + noise2) * 0.5
  );

  // Apply color adjustments
  color = applyHueShift(color, hueShift);
  color = applySaturation(color, saturation);
  color = applyLightness(color, lightness);

  return vec4(color, 1.0);
}
`,f2:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord / iResolution.xy;
  float aspectRatio = iResolution.x / iResolution.y;
  float t = iTime * timeScale;

  // Create multiple layers of fluid movement with aspect ratio correction
  vec2 p1 = uv * 1.0;
  p1.x *= aspectRatio;
  vec2 p2 = uv * 1.5;
  p2.x *= aspectRatio;

  // Generate noise at different scales with lower frequency
  float noise1 = noise(p1 + t * 0.05);
  float noise2 = noise(p2 - t * 0.08);
  float noise3 = noise(p1 * 0.5 + t * 0.1);

  // Combine noise layers
  float combinedNoise = (noise1 + noise2 + noise3) / 3.0;

  // Create color based on noise
  vec3 color = vec3(
    noise1 * 0.7 + 0.3,
    noise2 * 0.7 + 0.3,
    combinedNoise * 0.7 + 0.3
  );

  // Add some rotation-based variation
  vec2 rotatedUV = uv * rot(t * 0.05);
  rotatedUV.x *= aspectRatio;
  float rotationNoise = noise(rotatedUV * 0.5);
  color = mix(color, color.yzx, rotationNoise * 0.3);

  // Apply color adjustments
  color = applyHueShift(color, hueShift);
  color = applySaturation(color, saturation);
  color = applyLightness(color, lightness);

  return vec4(color, 1.0);
}
`,f3:`
vec4 shader(vec2 fragCoord) {
  vec2 uv = fragCoord / iResolution.xy;
  float aspectRatio = iResolution.x / iResolution.y;
  float t = iTime * timeScale;

  // Create abstract fluid movement with aspect ratio correction
  vec2 p = uv * 1.0;
  p.x *= aspectRatio;
  p = p * rot(t * 0.02);

  // Generate multiple noise layers with lower frequency
  float noise1 = noise(p + t * 0.05);
  float noise2 = noise(p * 0.5 - t * 0.08);
  float noise3 = noise(p * 0.25 + t * 0.1);

  // Create color channels with different noise combinations
  vec3 color = vec3(
    noise1 * noise2,
    noise2 * noise3,
    noise3 * noise1
  );

  // Add some movement-based variation
  vec2 movement = vec2(sin(t * 0.1), cos(t * 0.15)) * 0.2;
  movement.x *= aspectRatio;
  float movementNoise = noise(uv + movement);
  color = mix(color, color.zxy, movementNoise);

  // Apply color adjustments
  color = applyHueShift(color, hueShift);
  color = applySaturation(color, saturation);
  color = applyLightness(color, lightness);

  return vec4(color, 1.0);
}
`,n1:`
vec3 irri(float hue) {
    return 0.5 + 0.5 * cos((9.0 * hue) + vec3(0.0, 23.0, 21.0));
}

vec2 line(vec2 p, vec2 a, vec2 b) {
    vec2 ba = b - a;
    vec2 pa = p - a;
    float h = clamp(dot(pa, ba) / dot(ba, ba), 0.0, 1.0);
    return vec2(length(pa - h * ba), h);
}

vec4 shader(vec2 fragCoord) {
    vec2 uv = fragCoord / iResolution.xy;
    float aspectRatio = iResolution.x / iResolution.y;
    float t = iTime * timeScale;

    // Create noise-based gradient with aspect ratio correction
    vec2 p = uv * 0.5;
    p.x *= aspectRatio;
    float noise1 = noise(p + t * 0.05);
    float noise2 = noise(p * 0.5 - t * 0.08);

    // Create gradient with noise influence
    vec3 color = vec3(
        noise1 * 0.8 + 0.2,
        noise2 * 0.8 + 0.2,
        (noise1 + noise2) * 0.4 + 0.3
    );

    // Add some rotation-based variation
    vec2 rotatedUV = uv * rot(t * 0.02);
    rotatedUV.x *= aspectRatio;
    float rotationNoise = noise(rotatedUV * 0.5);
    color = mix(color, color.yzx, rotationNoise * 0.2);

    // Apply color adjustments
    color = applyHueShift(color, hueShift);
    color = applySaturation(color, saturation);
    color = applyLightness(color, lightness);

    return vec4(color, 1.0);
}
`,n2:`
vec4 shader(vec2 fragCoord) {
    vec2 uv = fragCoord / iResolution.xy;
    float aspectRatio = iResolution.x / iResolution.y;
    float t = iTime * timeScale;

    // Create complex noise pattern with aspect ratio correction
    vec2 p1 = uv * 0.5;
    p1.x *= aspectRatio;
    vec2 p2 = uv * 0.75;
    p2.x *= aspectRatio;

    // Generate multiple noise layers with lower frequency
    float noise1 = noise(p1 + t * 0.05);
    float noise2 = noise(p2 - t * 0.08);
    float noise3 = noise(p1 * 0.25 + t * 0.1);

    // Combine noise layers with different weights
    float combinedNoise = (noise1 * 0.4 + noise2 * 0.3 + noise3 * 0.3);

    // Create color with noise influence
    vec3 color = vec3(
        noise1 * 0.6 + 0.4,
        noise2 * 0.6 + 0.4,
        combinedNoise * 0.6 + 0.4
    );

    // Add some movement-based variation
    vec2 movement = vec2(sin(t * 0.1), cos(t * 0.15)) * 0.2;
    movement.x *= aspectRatio;
    float movementNoise = noise(uv + movement);
    color = mix(color, color.zxy, movementNoise * 0.3);

    // Apply color adjustments
    color = applyHueShift(color, hueShift);
    color = applySaturation(color, saturation);
    color = applyLightness(color, lightness);

    return vec4(color, 1.0);
}
`};Object.keys(r);let a=e=>Math.round(17*Number.parseInt(e,16)),n=(e,t,o,i=2)=>{let r=Math.max(0,Math.min(e,15));return 0===r?t:t+((r-1)/14)**i*(o-t)},l=`#version 300 es
      in vec2 position;
      void main() {
          gl_Position = vec4(position, 0.0, 1.0);
      }`;class s{#e;#t;#o;#i;#r;#a;#n;#l;#s;#c;#u;#d;#f;static #v=50;constructor(e,t,o){this.#t=e,this.#c=t,this.#u=l,this.#r=.4,this.#a=!1,this.#l=o,this.#n=o[1],this.#s={speed:0,hueShift:0,saturation:0,lightness:0},this.#d=null,this.#f=0,this.#p()}#p(){this.#t.addEventListener("webglcontextlost",e=>{e.preventDefault(),this.#a=!1,this.#e=null,this.#o=null,this.#i=null,this.#t&&(this.#t.width=0)}),this.#t.addEventListener("webglcontextrestored",()=>{this.init()})}init(){this.#e=this.#h(this.#t),this.#o=this.#m(this.#u,this.#c),this.#i=this.#g(),this.#a=!0,this.#y(),this.#x(),this.#b(!0),this.#S()}#h(e){let t=e.getContext("webgl2",{antialias:!0});if(!t)throw Error("WebGL2 not supported");return t}#w(e,t){let o=this.#e.createShader(e);this.#e.shaderSource(o,t),this.#e.compileShader(o);let i=this.#e.getShaderInfoLog(o);if(i)throw Error(`${e===this.#e.VERTEX_SHADER?"Vertex":"Fragment"} shader compilation error: ${i}`);return o}#m(e,t){let o=this.#e.createProgram(),i=this.#w(this.#e.VERTEX_SHADER,e),r=this.#w(this.#e.FRAGMENT_SHADER,t);this.#e.attachShader(o,i),this.#e.attachShader(o,r),this.#e.linkProgram(o);let a=this.#e.getProgramInfoLog(o);return a&&console.error("Program linking error:",a),this.#e.detachShader(o,i),this.#e.detachShader(o,r),this.#e.deleteShader(i),this.#e.deleteShader(r),this.#e.useProgram(o),o}#y(){let e=this.#e.createBuffer();this.#e.bindBuffer(this.#e.ARRAY_BUFFER,e),this.#e.bufferData(this.#e.ARRAY_BUFFER,new Float32Array([-1,-1,1,-1,-1,1,1,1]),this.#e.STATIC_DRAW)}#x(){let e=this.#e.getAttribLocation(this.#o,"position");this.#e.enableVertexAttribArray(e),this.#e.vertexAttribPointer(e,2,this.#e.FLOAT,!1,0,0)}#g(){return{iResolution:this.#e.getUniformLocation(this.#o,"iResolution"),iTime:this.#e.getUniformLocation(this.#o,"iTime"),iFrame:this.#e.getUniformLocation(this.#o,"iFrame"),options:this.#e.getUniformLocation(this.#o,"options"),timeScale:this.#e.getUniformLocation(this.#o,"timeScale"),hueShift:this.#e.getUniformLocation(this.#o,"hueShift"),saturation:this.#e.getUniformLocation(this.#o,"saturation"),lightness:this.#e.getUniformLocation(this.#o,"lightness")}}#b(e=!1){if(!this.#n)return;this.#e.useProgram(this.#o),this.#e.uniform1iv(this.#i.options,this.#n);let[t,o,i,r]=this.#n.map(e=>Math.round(15*e/255)),[a,l,s,c]=[n(t,.1,3,1.5),o/15,n(i,.3,3,1.5),r/15];(e||a!==this.#s.speed||l!==this.#s.hueShift||s!==this.#s.saturation||c!==this.#s.lightness)&&(this.#e.uniform1f(this.#i.timeScale,a),this.#e.uniform1f(this.#i.hueShift,l),this.#e.uniform1f(this.#i.saturation,s),this.#e.uniform1f(this.#i.lightness,c),this.#s={speed:a,hueShift:l,saturation:s,lightness:c})}updateSeed(e){return!(e[0]===this.#l[0]&&e[1].every((e,t)=>e===this.#l[1][t]))&&(this.#l=e,this.#n=e[1],this.#b(!0),!0)}#C(e){if(!this.#a||!this.#t||!this.#e)return;let{iResolution:t,iTime:o,iFrame:i}=this.#i;this.#e.useProgram(this.#o);let r=this.#t.clientWidth,a=this.#t.clientHeight;(this.#t.width!==r||this.#t.height!==a)&&(this.#t.width=r,this.#t.height=a,this.#e.uniform3f(t,this.#t.width,this.#t.height,1),this.#e.viewport(0,0,this.#t.width,this.#t.height));let n=e/1e3;this.#e.uniform1f(o,n),this.#e.uniform1f(i,Math.floor(60*n))}#R(e){if(null!==this.#d){let t=Math.min(e-this.#d,s.#v);this.#f+=t}this.#d=e}#S(){let e=()=>{if(!this.#a||!this.#t||!this.#e)return;let t=performance.now();this.#R(t),this.#C(this.#f),this.#e.drawArrays(this.#e.TRIANGLE_STRIP,0,4),requestAnimationFrame(e)};requestAnimationFrame(e)}destroy(){if(this.#a=!1,this.#o&&this.#e&&this.#e.deleteProgram(this.#o),this.#t)try{this.#t.remove()}catch(e){}this.#o=null,this.#t=null,this.#e=null}}let c=`
  void main() {
    fragColor = shader(gl_FragCoord.xy);
  }
  `,u=null;async function d(e,t="body"){if(!e)throw Error("Seed is required");let o=[e.split(".").shift(),new Uint8Array(e.split(".").pop().split("").map(a))],[n]=o;if(u?.shaderId===n)return u.updateSeed(o),u;u&&(u.destroy(),u=null);let[l,f]=await Promise.all([Promise.resolve(i),Promise.resolve(r[n])]),v=new s(((e="body")=>{let t=document.querySelector(e)??document.body;return"CANVAS"===t.tagName?t:t.appendChild(Object.assign(document.createElement("canvas"),{id:"gradient-gl",style:"position:fixed;inset:0;width:100vw;height:100vh;z-index:-1;pointer-events:none;"}))})(t),l+f+c,o);return v.shaderId=n,v.init(),u=v,v}let f=new URL("file:///home/<USER>/Projects/AlbatrosDoc/node_modules/gradient-gl/index.js"),v=f.searchParams.get("seed"),p=f.searchParams.get("selector")||"body";v&&d(v,p)},2464:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return i}});let i=o(8229)._(o(2115)).default.createContext({})},3063:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return b}});let i=o(8229),r=o(6966),a=o(5155),n=r._(o(2115)),l=i._(o(7650)),s=i._(o(5564)),c=o(8883),u=o(5840),d=o(6752);o(3230);let f=o(901),v=i._(o(1193)),p=o(6654),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function m(e,t,o,i,r,a,n){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&r(!0),null==o?void 0:o.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let i=!1,r=!1;o.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>i,isPropagationStopped:()=>r,persist:()=>{},preventDefault:()=>{i=!0,t.preventDefault()},stopPropagation:()=>{r=!0,t.stopPropagation()}})}(null==i?void 0:i.current)&&i.current(e)}}))}function g(e){return n.use?{fetchPriority:e}:{fetchpriority:e}}let y=(0,n.forwardRef)((e,t)=>{let{src:o,srcSet:i,sizes:r,height:l,width:s,decoding:c,className:u,style:d,fetchPriority:f,placeholder:v,loading:h,unoptimized:y,fill:x,onLoadRef:b,onLoadingCompleteRef:S,setBlurComplete:w,setShowAltText:C,sizesInput:R,onLoad:_,onError:A,...j}=e,P=(0,n.useCallback)(e=>{e&&(A&&(e.src=e.src),e.complete&&m(e,v,b,S,w,y,R))},[o,v,b,S,w,A,y,R]),E=(0,p.useMergedRef)(t,P);return(0,a.jsx)("img",{...j,...g(f),loading:h,width:s,height:l,decoding:c,"data-nimg":x?"fill":"1",className:u,style:d,sizes:r,srcSet:i,src:o,ref:E,onLoad:e=>{m(e.currentTarget,v,b,S,w,y,R)},onError:e=>{C(!0),"empty"!==v&&w(!0),A&&A(e)}})});function x(e){let{isAppRouter:t,imgAttributes:o}=e,i={as:"image",imageSrcSet:o.srcSet,imageSizes:o.sizes,crossOrigin:o.crossOrigin,referrerPolicy:o.referrerPolicy,...g(o.fetchPriority)};return t&&l.default.preload?(l.default.preload(o.src,i),null):(0,a.jsx)(s.default,{children:(0,a.jsx)("link",{rel:"preload",href:o.srcSet?void 0:o.src,...i},"__nimg-"+o.src+o.srcSet+o.sizes)})}let b=(0,n.forwardRef)((e,t)=>{let o=(0,n.useContext)(f.RouterContext),i=(0,n.useContext)(d.ImageConfigContext),r=(0,n.useMemo)(()=>{var e;let t=h||i||u.imageConfigDefault,o=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),r=t.deviceSizes.sort((e,t)=>e-t),a=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:o,deviceSizes:r,qualities:a}},[i]),{onLoad:l,onLoadingComplete:s}=e,p=(0,n.useRef)(l);(0,n.useEffect)(()=>{p.current=l},[l]);let m=(0,n.useRef)(s);(0,n.useEffect)(()=>{m.current=s},[s]);let[g,b]=(0,n.useState)(!1),[S,w]=(0,n.useState)(!1),{props:C,meta:R}=(0,c.getImgProps)(e,{defaultLoader:v.default,imgConf:r,blurComplete:g,showAltText:S});return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y,{...C,unoptimized:R.unoptimized,placeholder:R.placeholder,fill:R.fill,onLoadRef:p,onLoadingCompleteRef:m,setBlurComplete:b,setShowAltText:w,sizesInput:e.sizes,ref:t}),R.priority?(0,a.jsx)(x,{isAppRouter:!o,imgAttributes:C}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5029:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let i=o(2115),r=i.useLayoutEffect,a=i.useEffect;function n(e){let{headManager:t,reduceComponentsToState:o}=e;function n(){if(t&&t.mountedInstances){let r=i.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(o(r,e))}}return r(()=>{var o;return null==t||null==(o=t.mountedInstances)||o.add(e.children),()=>{var o;null==t||null==(o=t.mountedInstances)||o.delete(e.children)}}),r(()=>(t&&(t._pendingUpdate=n),()=>{t&&(t._pendingUpdate=n)})),a(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{function o(e){let{widthInt:t,heightInt:o,blurWidth:i,blurHeight:r,blurDataURL:a,objectFit:n}=e,l=i?40*i:t,s=r?40*r:o,c=l&&s?"viewBox='0 0 "+l+" "+s+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return o}})},5564:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var o in t)Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}(t,{default:function(){return h},defaultHead:function(){return d}});let i=o(8229),r=o(6966),a=o(5155),n=r._(o(2115)),l=i._(o(5029)),s=o(2464),c=o(2830),u=o(7544);function d(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===n.default.Fragment?e.concat(n.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}o(3230);let v=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:o}=t;return e.reduce(f,[]).reverse().concat(d(o).reverse()).filter(function(){let e=new Set,t=new Set,o=new Set,i={};return r=>{let a=!0,n=!1;if(r.key&&"number"!=typeof r.key&&r.key.indexOf("$")>0){n=!0;let t=r.key.slice(r.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(r.type){case"title":case"base":t.has(r.type)?a=!1:t.add(r.type);break;case"meta":for(let e=0,t=v.length;e<t;e++){let t=v[e];if(r.props.hasOwnProperty(t))if("charSet"===t)o.has(t)?a=!1:o.add(t);else{let e=r.props[t],o=i[t]||new Set;("name"!==t||!n)&&o.has(e)?a=!1:(o.add(e),i[t]=o)}}}return a}}()).reverse().map((e,t)=>{let o=e.key||t;return n.default.cloneElement(e,{key:o})})}let h=function(e){let{children:t}=e,o=(0,n.useContext)(s.AmpStateContext),i=(0,n.useContext)(c.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:p,headManager:i,inAmpMode:(0,u.isInAmpMode)(o),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5695:(e,t,o)=>{var i=o(8999);o.o(i,"usePathname")&&o.d(t,{usePathname:function(){return i.usePathname}}),o.o(i,"useRouter")&&o.d(t,{useRouter:function(){return i.useRouter}})},5840:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var o in t)Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}(t,{VALID_LOADERS:function(){return o},imageConfigDefault:function(){return i}});let o=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},6654:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let i=o(2115);function r(e,t){let o=(0,i.useRef)(null),r=(0,i.useRef)(null);return(0,i.useCallback)(i=>{if(null===i){let e=o.current;e&&(o.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(o.current=a(e,i)),t&&(r.current=a(t,i))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let o=e(t);return"function"==typeof o?o:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6752:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return a}});let i=o(8229)._(o(2115)),r=o(5840),a=i.default.createContext(r.imageConfigDefault)},6766:(e,t,o)=>{o.d(t,{default:()=>r.a});var i=o(1469),r=o.n(i)},7544:(e,t)=>{function o(e){let{ampFirst:t=!1,hybrid:o=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||o&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return o}})},7652:(e,t,o)=>{o.d(t,{c3:()=>a});var i=o(2550);function r(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let a=r(0,i.c3);r(0,i.kc)},8883:(e,t,o)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),o(3230);let i=o(5100),r=o(5840),a=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var o,s;let c,u,d,{src:f,sizes:v,unoptimized:p=!1,priority:h=!1,loading:m,className:g,quality:y,width:x,height:b,fill:S=!1,style:w,overrideSrc:C,onLoad:R,onLoadingComplete:_,placeholder:A="empty",blurDataURL:j,fetchPriority:P,decoding:E="async",layout:O,objectFit:T,objectPosition:U,lazyBoundary:M,lazyRoot:z,...L}=e,{imgConf:k,showAltText:I,blurComplete:q,defaultLoader:F}=t,N=k||r.imageConfigDefault;if("allSizes"in N)c=N;else{let e=[...N.deviceSizes,...N.imageSizes].sort((e,t)=>e-t),t=N.deviceSizes.sort((e,t)=>e-t),i=null==(o=N.qualities)?void 0:o.sort((e,t)=>e-t);c={...N,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===F)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=L.loader||F;delete L.loader,delete L.srcSet;let D="__next_img_default"in B;if(D){if("custom"===c.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:o,...i}=t;return e(i)}}if(O){"fill"===O&&(S=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[O];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[O];t&&!v&&(v=t)}let H="",V=l(x),G=l(b);if((s=f)&&"object"==typeof s&&(n(s)||void 0!==s.src)){let e=n(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(u=e.blurWidth,d=e.blurHeight,j=j||e.blurDataURL,H=e.src,!S)if(V||G){if(V&&!G){let t=V/e.width;G=Math.round(e.height*t)}else if(!V&&G){let t=G/e.height;V=Math.round(e.width*t)}}else V=e.width,G=e.height}let W=!h&&("lazy"===m||void 0===m);(!(f="string"==typeof f?f:H)||f.startsWith("data:")||f.startsWith("blob:"))&&(p=!0,W=!1),c.unoptimized&&(p=!0),D&&!c.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(p=!0);let Y=l(y),K=Object.assign(S?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:T,objectPosition:U}:{},I?{}:{color:"transparent"},w),X=q||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:V,heightInt:G,blurWidth:u,blurHeight:d,blurDataURL:j||"",objectFit:K.objectFit})+'")':'url("'+A+'")',$=a.includes(K.objectFit)?"fill"===K.objectFit?"100% 100%":"cover":K.objectFit,J=X?{backgroundSize:$,backgroundPosition:K.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},Q=function(e){let{config:t,src:o,unoptimized:i,width:r,quality:a,sizes:n,loader:l}=e;if(i)return{src:o,srcSet:void 0,sizes:void 0};let{widths:s,kind:c}=function(e,t,o){let{deviceSizes:i,allSizes:r}=e;if(o){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(o);)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:r.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:r,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>r.find(t=>t>=e)||r[r.length-1]))],kind:"x"}}(t,r,n),u=s.length-1;return{sizes:n||"w"!==c?n:"100vw",srcSet:s.map((e,i)=>l({config:t,src:o,quality:a,width:e})+" "+("w"===c?e:i+1)+c).join(", "),src:l({config:t,src:o,quality:a,width:s[u]})}}({config:c,src:f,unoptimized:p,width:V,quality:Y,sizes:v,loader:B});return{props:{...L,loading:W?"lazy":m,fetchPriority:P,width:V,height:G,decoding:E,className:g,style:{...K,...J},sizes:Q.sizes,srcSet:Q.srcSet,src:C||Q.src},meta:{unoptimized:p,priority:h,placeholder:A,fill:S}}}}}]);