/* [next]/internal/font/google/urbanist_a417c42a.module.css [app-client] (css) */
@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 800;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/f680eb953b2dadd7-s.e9e9dddc.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Urbanist;
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../media/2314d929b596fa94-s.p.b88a81e7.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Urbanist Fallback;
  src: local(Arial);
  ascent-override: 95.93%;
  descent-override: 25.24%;
  line-gap-override: 0.0%;
  size-adjust: 99.04%;
}

.urbanist_a417c42a-module__0n8ymG__className {
  font-family: Urbanist, Urbanist Fallback;
  font-style: normal;
}

.urbanist_a417c42a-module__0n8ymG__variable {
  --font-urbanist: "Urbanist", "Urbanist Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_urbanist_a417c42a_module_css_e59ae46c._.single.css.map*/