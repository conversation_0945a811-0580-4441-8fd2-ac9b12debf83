{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/urbanist_a417c42a.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"urbanist_a417c42a-module__0n8ymG__className\",\n  \"variable\": \"urbanist_a417c42a-module__0n8ymG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/urbanist_a417c42a.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Urbanist%22,%22arguments%22:[{%22variable%22:%22--font-urbanist%22,%22subsets%22:[%22latin%22],%22weight%22:[%22100%22,%22200%22,%22300%22,%22400%22,%22500%22,%22600%22,%22700%22,%22800%22,%22900%22]}],%22variableName%22:%22urbanist%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Urbanist', 'Urbanist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/i18n/request.ts"], "sourcesContent": ["import {getRequestConfig} from 'next-intl/server';\n\n// Can be imported from a shared config\nconst locales = ['bs', 'en', 'de'] as const;\n\nexport default getRequestConfig(async ({requestLocale}) => {\n  // This typically corresponds to the `[locale]` segment\n  let locale = await requestLocale;\n\n  // Ensure that a valid locale is used\n  if (!locale || !locales.includes(locale as typeof locales[number])) {\n    locale = 'bs';\n  }\n\n  return {\n    locale,\n    messages: (await import(`../../messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;;AAEA,uCAAuC;AACvC,MAAM,UAAU;IAAC;IAAM;IAAM;CAAK;uCAEnB,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAC,aAAa,EAAC;IACpD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,CAAC,SAAmC;QAClE,SAAS;IACX;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/AlbatrosDoc/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Urbanist } from \"next/font/google\";\nimport { NextIntlClientProvider } from 'next-intl';\nimport { getMessages } from 'next-intl/server';\nimport \"./globals.css\";\n\nconst urbanist = Urbanist({\n  variable: \"--font-urbanist\",\n  subsets: [\"latin\"],\n  weight: [\"100\", \"200\", \"300\", \"400\", \"500\", \"600\", \"700\", \"800\", \"900\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"AlbatrosDoc\",\n  description: \"Pouzdana rješenja za vašu dokumentaciju i administrativne potrebe\",\n};\n\nexport default async function LocaleLayout({\n  children,\n  params\n}: {\n  children: React.ReactNode;\n  params: Promise<{ locale: string }>;\n}) {\n  const { locale } = await params;\n  const messages = await getMessages();\n\n  return (\n    <html lang={locale}>\n      <body\n        className={`${urbanist.variable} antialiased font-urbanist`}\n      >\n        <NextIntlClientProvider messages={messages}>\n          {children}\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;;;;AASO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EAIP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC;QAAK,MAAM;kBACV,cAAA,8OAAC;YACC,WAAW,GAAG,4IAAA,CAAA,UAAQ,CAAC,QAAQ,CAAC,0BAA0B,CAAC;sBAE3D,cAAA,8OAAC,kQAAA,CAAA,yBAAsB;gBAAC,UAAU;0BAC/B;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}