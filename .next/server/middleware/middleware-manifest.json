{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(de|en)/:path*{(\\\\.json)}?", "originalSource": "/(de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "982e0b55c2a72b0687d5904e71caa999", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "53425c2188a55b015275c2e6dc1ad3287c4fde0af1654c4f5d8b7f2e256c4f4b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "82fa9b287b4fd5fdc4c53f84136093f0ad4d3a413355f40bd1cc45c548bf2a24"}}}, "instrumentation": null, "functions": {}}