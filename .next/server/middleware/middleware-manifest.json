{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(bs|de|en)/:path*{(\\\\.json)}?", "originalSource": "/(bs|de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "50f34c103c1e2b3e1ce5df8e3e40732e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5a79baae27aa0e62547eacda82367d3ddb5b251ba844df36a15e27e05f799924", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3d7d5201505dfe195b186ac05f105d6ad3e6f04e3e07aa9e2fa3151048974e7e"}}}, "instrumentation": null, "functions": {}}