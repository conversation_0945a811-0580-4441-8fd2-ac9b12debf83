{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1ff2e906._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_835ea4b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(de|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(de|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "mBPkuLSKcMTGczlXPu79aNpGNxUol+OqU0kgv7T36PE=", "__NEXT_PREVIEW_MODE_ID": "a8759ca55adf47bc88eaa03116e32b98", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "79d6c3a68512dbaa082541cf498531064e3ddedbded83fd4699d4555f07b516b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "994743f69a70074153148c66aeefc484eff028974ad6d8c87733e6db949e6600"}}}, "sortedMiddleware": ["/"], "functions": {}}