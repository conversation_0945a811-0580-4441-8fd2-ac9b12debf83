[{"/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/layout.tsx": "1", "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/page.tsx": "2", "/home/<USER>/Projects/AlbatrosDoc/src/app/layout.tsx": "3", "/home/<USER>/Projects/AlbatrosDoc/src/components/FeaturesSection.tsx": "4", "/home/<USER>/Projects/AlbatrosDoc/src/components/Footer.tsx": "5", "/home/<USER>/Projects/AlbatrosDoc/src/components/HeroSection.tsx": "6", "/home/<USER>/Projects/AlbatrosDoc/src/components/Navigation.tsx": "7", "/home/<USER>/Projects/AlbatrosDoc/src/i18n/request.ts": "8", "/home/<USER>/Projects/AlbatrosDoc/src/middleware.ts": "9", "/home/<USER>/Projects/AlbatrosDoc/src/components/AboutSection.tsx": "10", "/home/<USER>/Projects/AlbatrosDoc/src/components/CTASection.tsx": "11", "/home/<USER>/Projects/AlbatrosDoc/src/types/gradient-gl.d.ts": "12"}, {"size": 1005, "mtime": 1755087793402, "results": "13", "hashOfConfig": "14"}, {"size": 571, "mtime": 1755086495650, "results": "15", "hashOfConfig": "14"}, {"size": 298, "mtime": 1755084901171, "results": "16", "hashOfConfig": "14"}, {"size": 5842, "mtime": 1755086867717, "results": "17", "hashOfConfig": "14"}, {"size": 13716, "mtime": 1755092559355, "results": "18", "hashOfConfig": "14"}, {"size": 2711, "mtime": 1755096055983, "results": "19", "hashOfConfig": "14"}, {"size": 9181, "mtime": 1755094159720, "results": "20", "hashOfConfig": "14"}, {"size": 532, "mtime": 1755085757690, "results": "21", "hashOfConfig": "14"}, {"size": 687, "mtime": 1755094467644, "results": "22", "hashOfConfig": "14"}, {"size": 6968, "mtime": 1755086826664, "results": "23", "hashOfConfig": "14"}, {"size": 8115, "mtime": 1755092019357, "results": "24", "hashOfConfig": "14"}, {"size": 365, "mtime": 1755096145792, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gypqbj", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/layout.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/page.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/app/layout.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/FeaturesSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/Footer.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/HeroSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/Navigation.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/i18n/request.ts", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/middleware.ts", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/AboutSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/CTASection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/types/gradient-gl.d.ts", [], []]