[{"/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/layout.tsx": "1", "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/page.tsx": "2", "/home/<USER>/Projects/AlbatrosDoc/src/app/layout.tsx": "3", "/home/<USER>/Projects/AlbatrosDoc/src/components/FeaturesSection.tsx": "4", "/home/<USER>/Projects/AlbatrosDoc/src/components/Footer.tsx": "5", "/home/<USER>/Projects/AlbatrosDoc/src/components/HeroSection.tsx": "6", "/home/<USER>/Projects/AlbatrosDoc/src/components/Navigation.tsx": "7", "/home/<USER>/Projects/AlbatrosDoc/src/i18n/request.ts": "8", "/home/<USER>/Projects/AlbatrosDoc/src/middleware.ts": "9", "/home/<USER>/Projects/AlbatrosDoc/src/components/AboutSection.tsx": "10", "/home/<USER>/Projects/AlbatrosDoc/src/components/CTASection.tsx": "11"}, {"size": 1005, "mtime": 1755087793402, "results": "12", "hashOfConfig": "13"}, {"size": 571, "mtime": 1755086495650, "results": "14", "hashOfConfig": "13"}, {"size": 298, "mtime": 1755084901171, "results": "15", "hashOfConfig": "13"}, {"size": 5842, "mtime": 1755086867717, "results": "16", "hashOfConfig": "13"}, {"size": 13704, "mtime": 1755087753544, "results": "17", "hashOfConfig": "13"}, {"size": 4582, "mtime": 1755091986301, "results": "18", "hashOfConfig": "13"}, {"size": 8467, "mtime": 1755087834840, "results": "19", "hashOfConfig": "13"}, {"size": 532, "mtime": 1755085757690, "results": "20", "hashOfConfig": "13"}, {"size": 333, "mtime": 1755084823352, "results": "21", "hashOfConfig": "13"}, {"size": 6968, "mtime": 1755086826664, "results": "22", "hashOfConfig": "13"}, {"size": 8115, "mtime": 1755092019357, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gypqbj", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/layout.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/app/[locale]/page.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/app/layout.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/FeaturesSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/Footer.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/HeroSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/Navigation.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/i18n/request.ts", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/middleware.ts", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/AboutSection.tsx", [], [], "/home/<USER>/Projects/AlbatrosDoc/src/components/CTASection.tsx", [], []]